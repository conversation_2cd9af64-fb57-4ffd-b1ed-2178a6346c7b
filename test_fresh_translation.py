#!/usr/bin/env python3
"""
Test script to verify fresh category translation with the new mappings.
"""

from fetch_data import translate_category_to_language

def test_new_translations():
    """Test the newly added category translations"""
    print("=" * 60)
    print("TESTING NEW CATEGORY TRANSLATIONS")
    print("=" * 60)
    
    # Test the newly added categories
    new_test_cases = [
        # New Arabic categories to English
        ("الجيلي والسكاكر", "english", "Gummies"),
        ("سائبة بالوزن", "english", "Loose Item"),
        
        # Verify existing translations still work
        ("سناكس", "english", "Snacks"),
        ("الفواكه المجففة", "english", "Dried Fruits"),
        
        # English to Arabic
        ("gummies", "arabic", "الحلوى المطاطية"),  # Should map to the main gummies category
        ("loose item", "arabic", "سائب"),  # Should map to the main loose category
    ]
    
    print("Testing new category translations:")
    print("-" * 60)
    
    all_passed = True
    for original, target_lang, expected in new_test_cases:
        result = translate_category_to_language(original, target_lang)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        
        if result != expected:
            all_passed = False
            print(f"{status} | '{original}' -> {target_lang} -> '{result}' (expected: '{expected}')")
        else:
            print(f"{status} | '{original}' -> {target_lang} -> '{result}'")
    
    print("-" * 60)
    if all_passed:
        print("🎉 All new category translations PASSED!")
    else:
        print("⚠️  Some new category translations FAILED!")
    
    return all_passed

def main():
    """Main test function"""
    print("🧪 TESTING FRESH CATEGORY TRANSLATIONS")
    print("=" * 60)
    
    # Test new translations
    success = test_new_translations()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if success:
        print("✅ All category translations are working correctly!")
        print("✅ The category grouping issue should now be resolved!")
        print("\n🔧 To verify with fresh data:")
        print("1. Delete any cached data files if they exist")
        print("2. Run: python generate_brochures.py english")
        print("3. Check that all categories are in English")
        print("4. Run: python generate_brochures.py arabic") 
        print("5. Check that all categories are in Arabic")
    else:
        print("❌ Some translations need to be fixed")

if __name__ == "__main__":
    main()
