#!/usr/bin/env python3
"""
Test script for category translation functionality.
This script tests the new category translation logic to ensure
Arabic and English categories are properly mapped.
"""

from fetch_data import get_category_translations, translate_category_to_language

def test_category_translations():
    """Test the category translation functionality"""
    print("=" * 60)
    print("TESTING CATEGORY TRANSLATION FUNCTIONALITY")
    print("=" * 60)
    
    # Test cases: (original_category, target_language, expected_result)
    test_cases = [
        # Arabic to English
        ("سناكس", "english", "Snacks"),
        ("الفواكه المجففة", "english", "Dried Fruits"),
        ("القهوة", "english", "Coffee"),
        ("معبأ", "english", "Pre-packed"),
        ("سائب", "english", "Loose Item"),
        
        # English to Arabic
        ("snacks", "arabic", "سناكس"),
        ("dried fruits", "arabic", "الفواكه المجففة"),
        ("coffee", "arabic", "القهوة"),
        ("pre-packed", "arabic", "معبأ"),
        ("loose item", "arabic", "سائب"),
        
        # Same language (should return as-is or properly formatted)
        ("سناكس", "arabic", "سناكس"),
        ("Snacks", "english", "Snacks"),
        
        # Unknown categories (should return original)
        ("Unknown Category", "english", "Unknown Category"),
        ("فئة غير معروفة", "arabic", "فئة غير معروفة"),
    ]
    
    print("Testing category translations:")
    print("-" * 60)
    
    all_passed = True
    for original, target_lang, expected in test_cases:
        result = translate_category_to_language(original, target_lang)
        status = "✅ PASS" if result == expected else "❌ FAIL"

        if result != expected:
            all_passed = False
            print(f"{status} | '{original}' -> {target_lang} -> '{result}' (expected: '{expected}') [DEBUG: original.lower()='{original.lower()}']")
        else:
            print(f"{status} | '{original}' -> {target_lang} -> '{result}' (expected: '{expected}')")
    
    print("-" * 60)
    if all_passed:
        print("🎉 All category translation tests PASSED!")
    else:
        print("⚠️  Some category translation tests FAILED!")
    
    print()
    return all_passed

def test_category_mappings():
    """Test that category mappings are comprehensive"""
    print("=" * 60)
    print("TESTING CATEGORY MAPPINGS COMPLETENESS")
    print("=" * 60)
    
    translations = get_category_translations()
    
    # Check for bidirectional mappings
    arabic_categories = ["سناكس", "الفواكه المجففة", "القهوة", "الحلويات", "البقوليات", "المكسرات والبذور", "الحلوى المطاطية", "معبأ", "سائب"]
    english_categories = ["snacks", "dried fruits", "coffee", "confectionery", "pulses", "nuts & seeds", "gummies", "pre-packed", "loose item"]
    
    print("Checking Arabic to English mappings:")
    for arabic_cat in arabic_categories:
        english_equivalent = translations.get(arabic_cat, "NOT FOUND")
        status = "✅" if english_equivalent != "NOT FOUND" else "❌"
        print(f"  {status} {arabic_cat} -> {english_equivalent}")
    
    print("\nChecking English to Arabic mappings:")
    for english_cat in english_categories:
        arabic_equivalent = translations.get(english_cat, "NOT FOUND")
        status = "✅" if arabic_equivalent != "NOT FOUND" else "❌"
        print(f"  {status} {english_cat} -> {arabic_equivalent}")
    
    print()

def main():
    """Main test function"""
    print("🧪 CATEGORY TRANSLATION TEST SUITE")
    print("=" * 60)
    
    # Run tests
    translation_passed = test_category_translations()
    test_category_mappings()
    
    # Summary
    print("=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if translation_passed:
        print("✅ Category translation functionality is working correctly!")
        print("✅ Ready to test with brochure generation")
    else:
        print("❌ Category translation has issues that need to be fixed")
        print("❌ Please review the translation logic before proceeding")
    
    print("\n🔧 Next steps:")
    print("1. Run: python generate_brochures.py english")
    print("2. Run: python generate_brochures.py arabic") 
    print("3. Compare the generated PDFs to verify category grouping")

if __name__ == "__main__":
    main()
