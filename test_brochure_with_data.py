#!/usr/bin/env python3
"""
Test script to generate brochure using existing res_full.json data.
This tests the new indexing system with real data.
"""

import json
import os
from fetch_data import get_meta_value, extract_category_index, extract_brand_index, parse_index_for_sorting
from template import create_brochure

def load_and_process_data(language="english"):
    """Load data from res_full.json and process it for brochure generation"""
    
    # Load the data
    with open("res_full.json", "r", encoding="utf-8") as f:
        all_products = json.load(f)
    
    print(f"Loaded {len(all_products)} products from res_full.json")
    
    # Process products for the brochure
    brochure_products = []
    processed_product_ids = set()
    
    for product in all_products:
        # Skip if we've already processed this product
        product_id = product.get('id')
        if product_id in processed_product_ids:
            continue
        processed_product_ids.add(product_id)
        
        # Process categories
        main_category = "Uncategorized"
        sub_category = None
        
        # Known subcategories (both Arabic and English)
        subcategory_names = ["Pre-packed", "Loose Item", "معبأ", "سائب"]
        
        if product['categories']:
            # Check if any of the categories is a known subcategory
            sub_cat_found = False
            for category in product['categories']:
                if category['name'] in subcategory_names:
                    sub_category = category['name']
                    sub_cat_found = True
                    break
            
            # If we found a subcategory, the main category is one of the others
            if sub_cat_found and len(product['categories']) > 1:
                for category in product['categories']:
                    if category['name'] not in subcategory_names:
                        main_category = category['name']
                        break
            # If no subcategory was found, use the first category as main
            elif not sub_cat_found and product['categories']:
                main_category = product['categories'][0]['name']
        
        # Extract category and brand indices from meta data
        category_meta = product.get('category_meta', [])
        brand_meta = product.get('brand_meta', [])
        
        # Get category index for the main category
        category_index = extract_category_index(category_meta, main_category)
        
        # Get image URL
        image_url = product['images'][0]['src'] if product['images'] else ""
        
        # Extract metadata
        packaging = get_meta_value(product['meta_data'], 'packaging')
        product_weight = get_meta_value(product['meta_data'], 'product_weight')

        # Get both English and Arabic brand names for index matching
        brand_name_english = get_meta_value(product['meta_data'], 'brand_name')
        brand_name_arabic = get_meta_value(product['meta_data'], 'brand_name_arabic')

        # Get language-specific fields
        if language.lower() == "arabic":
            product_name = get_meta_value(product['meta_data'], 'product_name_arabic') or product['name']
            brand_name = brand_name_arabic or brand_name_english
            country_of_origin = get_meta_value(product['meta_data'], 'country_of_origin_arabic') or get_meta_value(product['meta_data'], 'country_of_origin')
        else:
            product_name = get_meta_value(product['meta_data'], 'product_name') or product['name']
            brand_name = brand_name_english or brand_name_arabic
            country_of_origin = get_meta_value(product['meta_data'], 'country_of_origin')

        # Get brand index for the brand (try both English and Arabic names)
        brand_index = extract_brand_index(brand_meta, brand_name_english, brand_name_arabic)
        
        # Create a product entry for the brochure
        brochure_product = {
            "name": product_name,
            "weight": product_weight or "N/A",
            "packaging": packaging or "N/A",
            "country_of_origin": country_of_origin or "N/A",
            "brand": brand_name or "N/A",
            "image_url": image_url,
            "category": main_category,
            "subcategory": sub_category,
            "category_index": category_index,  # New: category index for sorting
            "brand_index": brand_index,  # New: brand index for sorting
            "is_packed": sub_category in ["Pre-packed", "معبأ"] if sub_category else False,
            "original_category": main_category,
            "original_subcategory": sub_category
        }
        
        brochure_products.append(brochure_product)
    
    # Sort products according to new indexing system: category index → brand index
    def sort_key(product):
        # Parse category index (PC100, LC200, etc.)
        category_index = product.get('category_index', '')
        cat_priority, cat_num = parse_index_for_sorting(category_index)
        
        # Parse brand index (B100, B200, etc.)
        brand_index = product.get('brand_index', '')
        brand_priority, brand_num = parse_index_for_sorting(brand_index)
        
        # Primary sort: Category priority (1=PC before 2=LC before 999=unindexed)
        # Secondary sort: Category number (100 before 200)
        # Tertiary sort: Brand number (100 before 200)
        return (cat_priority, cat_num, brand_num)
    
    brochure_products.sort(key=sort_key)
    
    # Print category summary for debugging
    categories_found = {}
    for product in brochure_products:
        category = product['category']
        category_index = product.get('category_index', 'No Index')
        key = f"{category} ({category_index})"
        categories_found[key] = categories_found.get(key, 0) + 1
    
    print(f"Processed {len(brochure_products)} products for the {language} brochure")
    print(f"Categories found (with indices): {list(categories_found.keys())}")
    for category_info, count in categories_found.items():
        print(f"  - {category_info}: {count} products")
    
    # Print first few products for debugging
    print(f"\nFirst 10 products (sorted by new indexing system):")
    for i, product in enumerate(brochure_products[:10]):
        print(f"  {i+1}. {product['name']} | Cat: {product['category']} ({product.get('category_index', 'No Index')}) | Brand: {product['brand']} ({product.get('brand_index', 'No Index')})")
    
    return brochure_products

def main():
    """Main test function"""
    print("🧪 TESTING NEW INDEXING SYSTEM WITH REAL DATA")
    print("=" * 60)

    # Test with English
    print("\n📊 Processing English data...")
    english_products = load_and_process_data("english")

    if english_products:
        print(f"\n📄 Generating English brochure with {len(english_products)} products...")
        output_file = "Test_Brochure_English_NewIndexing.pdf"

        try:
            create_brochure(output_file, english_products, language="english")

            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                print(f"✅ English brochure generated successfully!")
                print(f"   📄 File: {output_file}")
                print(f"   📏 Size: {file_size:.2f} MB")
            else:
                print("❌ English brochure file was not created")

        except Exception as e:
            print(f"❌ Error generating English brochure: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ No English products found")

    # Test with Arabic
    print("\n📊 Processing Arabic data...")
    arabic_products = load_and_process_data("arabic")

    if arabic_products:
        print(f"\n📄 Generating Arabic brochure with {len(arabic_products)} products...")
        output_file = "Test_Brochure_Arabic_NewIndexing.pdf"

        try:
            create_brochure(output_file, arabic_products, language="arabic")

            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                print(f"✅ Arabic brochure generated successfully!")
                print(f"   📄 File: {output_file}")
                print(f"   📏 Size: {file_size:.2f} MB")
            else:
                print("❌ Arabic brochure file was not created")

        except Exception as e:
            print(f"❌ Error generating Arabic brochure: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ No Arabic products found")

    print(f"\n🔍 Verification checklist:")
    print(f"   ✅ Products should be sorted PC before LC")
    print(f"   ✅ Brand-specific covers should be used")
    print(f"   ✅ Front pages (PAGE 0.jpg, etc.) should appear first")
    print(f"   ✅ End page (END PAGE.jpg) should appear last")
    print(f"   ✅ Brand indices should be extracted correctly")
    print(f"   ✅ Category indices should be used for sorting")

if __name__ == "__main__":
    main()
