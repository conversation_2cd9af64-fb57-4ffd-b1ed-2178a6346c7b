# Enhanced PDF Brochure Generator

This document describes the enhancements made to the PDF brochure generator to support multi-language content, improved layout, and better organization.

## 🆕 New Features

### 1. **Language Support & Filtering**
- **Arabic and English Support**: Generate brochures in either Arabic or English
- **Language-Specific Content**: Automatically uses appropriate product names, brands, and country of origin based on selected language
- **Arabic Font Support**: Proper RTL (Right-to-Left) text rendering with Arabic font fallbacks
- **Smart Fallbacks**: If Arabic content is missing, falls back to English content

### 2. **PDF Structure with Front/End Pages**
- **Front Cover Pages**: Automatically includes front cover images (`img/front-1.jpg` to `img/front-5.jpg`) as the first pages
- **End Page**: Adds final page using `img/end.jpg`
- **Full-Page Images**: Cover images fill the entire page for professional appearance

### 3. **Category Cover Images**
- **Automatic Category Covers**: Displays category-specific cover images before each category section
- **Smart Image Matching**: Uses fuzzy matching to associate category names with image files
- **Predefined Mappings**: Built-in mappings for common categories in both Arabic and English

### 4. **Enhanced Content Organization**
- **Hierarchical Sorting**: Products are sorted by:
  1. **Packaging Type**: Pre-packed items first, then loose items
  2. **Category**: Grouped by product category
  3. **Brand**: Sorted alphabetically by brand within each category
- **Improved Data Processing**: Better handling of category and subcategory relationships

### 5. **Robust Data Validation**
- **Fuzzy Matching**: Handles variations in category names and image filenames
- **Data Consistency**: Manages different capitalizations and spacing in data
- **Error Handling**: Graceful handling of missing images or data

## 📁 File Structure

```
brochure/
├── img/                          # Image folder
│   ├── front-1.jpg              # Front cover page 1
│   ├── front-2.jpg              # Front cover page 2
│   ├── front-3.jpg              # Front cover page 3
│   ├── front-4.jpg              # Front cover page 4
│   ├── front-5.jpg              # Front cover page 5
│   ├── end.jpg                  # End page
│   ├── dried-fruits.jpg         # Category cover: Dried Fruits
│   ├── snacks-.jpg              # Category cover: Snacks
│   ├── coffee.jpg               # Category cover: Coffee
│   ├── confectionery.jpg        # Category cover: Confectionery
│   ├── pulses.jpg               # Category cover: Pulses
│   ├── nuts-and-seeds.jpg       # Category cover: Nuts & Seeds
│   ├── gummies.jpg              # Category cover: Gummies
│   ├── pre-packed-snacks.jpg    # Subcategory: Pre-packed
│   └── loose-item-snacks.jpg    # Subcategory: Loose items
├── fonts/                       # Font folder (optional)
│   ├── Satoshi-Regular.ttf      # Main font
│   ├── Satoshi-Bold.ttf         # Bold font
│   └── NotoSansArabic-Regular.ttf # Arabic font (optional)
├── fetch_data.py                # Enhanced data fetching and processing
├── template.py                  # Enhanced PDF template with new features
├── generate_brochures.py        # Main generation script
├── test_enhanced_brochure.py    # Test script
└── res_full.json               # Product data
```

## 🚀 Usage

### Basic Usage

```bash
# Generate Arabic brochure (default)
python generate_brochures.py

# Generate English brochure
python generate_brochures.py english

# Generate both Arabic and English brochures
python generate_brochures.py both
```

### Programmatic Usage

```python
from fetch_data import generate_brochure

# Generate Arabic brochure
generate_brochure(output_file="Arabic_Brochure.pdf", language="arabic")

# Generate English brochure
generate_brochure(output_file="English_Brochure.pdf", language="english")
```

## 🔧 Configuration

### Category Image Mappings

The system includes predefined mappings for category names to image files:

```python
# Arabic categories
"سناكس": "snacks-.jpg"
"الفواكه المجففة": "dried-fruits.jpg"
"القهوة": "coffee.jpg"
"الحلويات": "confectionery.jpg"
"البقوليات": "pulses.jpg"
"المكسرات والبذور": "nuts-and-seeds.jpg"
"الحلوى المطاطية": "gummies.jpg"
"معبأ": "pre-packed-snacks.jpg"
"سائب": "loose-item-snacks.jpg"

# English categories
"snacks": "snacks-.jpg"
"dried fruits": "dried-fruits.jpg"
"coffee": "coffee.jpg"
"confectionery": "confectionery.jpg"
"pulses": "pulses.jpg"
"nuts & seeds": "nuts-and-seeds.jpg"
"gummies": "gummies.jpg"
"pre-packed": "pre-packed-snacks.jpg"
"loose": "loose-item-snacks.jpg"
```

### Font Configuration

The system automatically detects and uses Arabic fonts in this order:
1. `fonts/NotoSansArabic-Regular.ttf`
2. `fonts/Arial-Unicode.ttf`
3. `fonts/Tahoma.ttf`
4. System fonts (Windows: `C:/Windows/Fonts/tahoma.ttf`, macOS: Arial Unicode MS)
5. Fallback to default font

## 📊 Data Structure

### Enhanced Product Data

Each product now includes:

```python
{
    "name": "Product name (language-specific)",
    "weight": "Product weight",
    "packaging": "Packaging count",
    "country_of_origin": "Country (language-specific)",
    "brand": "Brand name (language-specific)",
    "image_url": "Product image URL",
    "category": "Main category",
    "subcategory": "Subcategory (Pre-packed/Loose)",
    "category_image": "Associated category image filename",
    "is_packed": "Boolean indicating if pre-packed"
}
```

## 🧪 Testing

Run the test script to verify all features:

```bash
python test_enhanced_brochure.py
```

The test script will:
- Check for required folders and files
- Test Arabic brochure generation
- Test English brochure generation
- Verify font availability
- Report on all new features

## 🔍 Troubleshooting

### Common Issues

1. **Arabic text not displaying correctly**
   - Ensure Arabic fonts are available
   - Check font installation in system or `fonts/` folder

2. **Category images not appearing**
   - Verify image files exist in `img/` folder
   - Check filename matching with category names
   - Review fuzzy matching threshold

3. **Front/end pages missing**
   - Ensure `img/front-*.jpg` and `img/end.jpg` exist
   - Check image file formats (JPG, JPEG, PNG supported)

4. **Products not sorted correctly**
   - Verify subcategory data in product metadata
   - Check brand name extraction

### Debug Mode

Enable debug output by modifying the scripts to include more verbose logging.

## 📈 Performance

- **Optimized Image Handling**: Images are cached locally to avoid repeated downloads
- **Efficient Sorting**: Products are sorted once before PDF generation
- **Memory Management**: Large datasets are processed in chunks

## 🔮 Future Enhancements

Potential improvements for future versions:
- Support for additional languages
- Custom category image mappings via configuration file
- Dynamic font selection based on content
- Advanced layout options
- Batch processing capabilities
