# Deployment Guide for Product Brochure Web App

This guide provides step-by-step instructions for deploying the Product Brochure web application to various free hosting platforms.

## Option 1: Render (Recommended)

[Render](https://render.com/) offers a generous free tier that's perfect for this application.

### Steps:

1. **Create a Render account**:
   - Go to [render.com](https://render.com/) and sign up for a free account

2. **Create a new Web Service**:
   - Click "New +" and select "Web Service"
   - Connect your GitHub/GitLab repository or use the "Upload" option

3. **Configure the service**:
   - Name: `product-brochure` (or any name you prefer)
   - Environment: `Python 3`
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `gunicorn app:app`
   - Select the free plan

4. **Deploy**:
   - Click "Create Web Service"
   - Render will automatically build and deploy your application

5. **Access your application**:
   - Once deployed, you can access your application at the URL provided by Render
   - Example: `https://product-brochure.onrender.com`

## Option 2: PythonAnywhere

[PythonAnywhere](https://www.pythonanywhere.com/) offers a free tier that's suitable for this application.

### Steps:

1. **Create a PythonAnywhere account**:
   - Go to [pythonanywhere.com](https://www.pythonanywhere.com/) and sign up for a free account

2. **Upload your code**:
   - Go to the "Files" tab
   - Create a new directory for your project (e.g., `product-brochure`)
   - Upload all your project files to this directory

3. **Set up a virtual environment**:
   - Go to the "Consoles" tab and start a new Bash console
   - Navigate to your project directory: `cd product-brochure`
   - Create a virtual environment: `python -m venv venv`
   - Activate the virtual environment: `source venv/bin/activate`
   - Install dependencies: `pip install -r requirements.txt`

4. **Configure a web app**:
   - Go to the "Web" tab and click "Add a new web app"
   - Select "Manual configuration" and choose Python 3.8 (or newer)
   - Set the path to your project directory
   - Configure the WSGI file to point to your Flask app

5. **Access your application**:
   - Your application will be available at `yourusername.pythonanywhere.com`

## Option 3: Fly.io

[Fly.io](https://fly.io/) offers a generous free tier with global deployment options.

### Steps:

1. **Install Flyctl**:
   - Follow the installation instructions at [fly.io/docs/hands-on/install-flyctl/](https://fly.io/docs/hands-on/install-flyctl/)

2. **Sign up and authenticate**:
   - Run `flyctl auth signup` to create an account
   - Or run `flyctl auth login` if you already have an account

3. **Initialize your app**:
   - Navigate to your project directory
   - Run `flyctl launch`
   - Follow the prompts to configure your application
   - When asked about a Postgres database, select "No"

4. **Deploy your app**:
   - Run `flyctl deploy`

5. **Access your application**:
   - Your application will be available at the URL provided by Fly.io
   - Example: `https://product-brochure.fly.dev`

## Option 4: Railway

[Railway](https://railway.app/) offers a limited free tier that's suitable for small applications.

### Steps:

1. **Create a Railway account**:
   - Go to [railway.app](https://railway.app/) and sign up using GitHub

2. **Create a new project**:
   - Click "New Project" and select "Deploy from GitHub repo"
   - Select your repository

3. **Configure the project**:
   - Railway will automatically detect that it's a Python application
   - Add the following environment variables if needed:
     - `PORT=8080`

4. **Deploy**:
   - Railway will automatically build and deploy your application

5. **Access your application**:
   - Click on your deployment and then on the "Domains" tab to find your URL

## Important Notes for All Deployments

1. **File Storage**:
   - These free hosting platforms typically have ephemeral file systems
   - This means files (like your generated PDF) might not persist between deployments
   - Consider using cloud storage (like AWS S3, Google Cloud Storage) for persistent file storage

2. **Environment Variables**:
   - Store sensitive information (API keys, credentials) as environment variables
   - Each platform has its own way to set environment variables

3. **Resource Limitations**:
   - Free tiers have limitations on resources (CPU, memory, bandwidth)
   - Your application might be put to sleep after periods of inactivity
   - The first request after inactivity might be slow (cold start)

4. **Custom Domains**:
   - Most free tiers allow you to use their subdomain only
   - Paid tiers usually allow custom domains with SSL certificates
