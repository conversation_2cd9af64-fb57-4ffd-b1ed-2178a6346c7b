#!/usr/bin/env python3
"""
Test script for the new indexing and sorting system.
This script tests the new category and brand indexing functionality.
"""

from fetch_data import parse_index_for_sorting, extract_category_index, extract_brand_index
from template import find_category_brand_cover_image
import os

def test_index_parsing():
    """Test the index parsing functionality"""
    print("=" * 60)
    print("TESTING INDEX PARSING FUNCTIONALITY")
    print("=" * 60)
    
    # Test cases: (index_string, expected_result)
    test_cases = [
        # Category indices
        ("PC100", (1, 100)),  # Packed - priority 1
        ("PC200", (1, 200)),  # Packed - priority 1
        ("LC100", (2, 100)),  # Loose - priority 2
        ("LC200", (2, 200)),  # Loose - priority 2
        ("C300", (2, 300)),   # Default to loose - priority 2

        # Brand indices
        ("B100", (0, 100)),   # Brand - priority 0
        ("B200", (0, 200)),   # Brand - priority 0
        ("B300", (0, 300)),   # Brand - priority 0

        # Edge cases
        ("", (999, 999999)),  # Empty string
        ("INVALID", (999, 999999)),  # Invalid format
        ("PC", (999, 999999)),  # Missing number
        ("B", (999, 999999)),  # Missing number
    ]
    
    print("Testing index parsing:")
    print("-" * 60)
    
    all_passed = True
    for index_str, expected in test_cases:
        result = parse_index_for_sorting(index_str)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        
        if result != expected:
            all_passed = False
            
        print(f"{status} | '{index_str}' -> {result} (expected: {expected})")
    
    print("-" * 60)
    if all_passed:
        print("🎉 All index parsing tests PASSED!")
    else:
        print("⚠️  Some index parsing tests FAILED!")
    
    return all_passed

def test_sorting_order():
    """Test that the sorting order works correctly"""
    print("\n" + "=" * 60)
    print("TESTING SORTING ORDER")
    print("=" * 60)
    
    # Create test products with different indices
    test_products = [
        {"category_index": "LC200", "brand_index": "B200", "name": "Product LC200-B200"},
        {"category_index": "PC100", "brand_index": "B100", "name": "Product PC100-B100"},
        {"category_index": "LC100", "brand_index": "B300", "name": "Product LC100-B300"},
        {"category_index": "PC100", "brand_index": "B200", "name": "Product PC100-B200"},
        {"category_index": "LC100", "brand_index": "B100", "name": "Product LC100-B100"},
        {"category_index": "", "brand_index": "B100", "name": "Product No-Index"},
    ]
    
    # Sort using the same logic as in the brochure generation
    def sort_key(product):
        category_index = product.get('category_index', '')
        brand_index = product.get('brand_index', '')
        cat_priority, cat_num = parse_index_for_sorting(category_index)
        brand_priority, brand_num = parse_index_for_sorting(brand_index)
        return (cat_priority, cat_num, brand_num)
    
    sorted_products = sorted(test_products, key=sort_key)
    
    print("Sorted products (should be PC before LC, then by numbers):")
    print("-" * 60)
    for i, product in enumerate(sorted_products):
        cat_idx = product.get('category_index', 'No Index')
        brand_idx = product.get('brand_index', 'No Index')
        print(f"{i+1}. {product['name']} | Cat: {cat_idx} | Brand: {brand_idx}")
    
    # Expected order: PC100-B100, PC100-B200, LC100-B100, LC100-B300, LC200-B200, No-Index
    expected_order = [
        "Product PC100-B100",
        "Product PC100-B200", 
        "Product LC100-B100",
        "Product LC100-B300",
        "Product LC200-B200",
        "Product No-Index"
    ]
    
    actual_order = [p['name'] for p in sorted_products]
    
    if actual_order == expected_order:
        print("\n✅ Sorting order is CORRECT!")
        return True
    else:
        print(f"\n❌ Sorting order is INCORRECT!")
        print(f"Expected: {expected_order}")
        print(f"Actual:   {actual_order}")
        return False

def test_cover_image_finding():
    """Test the cover image finding functionality"""
    print("\n" + "=" * 60)
    print("TESTING COVER IMAGE FINDING")
    print("=" * 60)
    
    # Check what cover images are available
    img_folder = "img"
    if os.path.exists(img_folder):
        cover_images = [f for f in os.listdir(img_folder) if f.endswith('.jpg') and '-' in f and not f.startswith('PAGE')]
        print(f"Available cover images: {cover_images}")
        
        # Test finding specific cover images
        test_cases = [
            ("PC100", "B100"),
            ("LC200", "B100"),
            ("PC300", "B300"),
            ("INVALID", "B100"),  # Should not find anything
        ]
        
        print("\nTesting cover image finding:")
        print("-" * 60)
        
        for cat_idx, brand_idx in test_cases:
            result = find_category_brand_cover_image(cat_idx, brand_idx, img_folder)
            status = "✅ FOUND" if result else "❌ NOT FOUND"
            print(f"{status} | {cat_idx}-{brand_idx} -> {result}")
    else:
        print(f"❌ Image folder '{img_folder}' not found")
        return False
    
    return True

def main():
    """Main test function"""
    print("🧪 NEW INDEXING SYSTEM TEST SUITE")
    print("=" * 60)
    
    # Run tests
    parsing_passed = test_index_parsing()
    sorting_passed = test_sorting_order()
    cover_passed = test_cover_image_finding()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    all_passed = parsing_passed and sorting_passed and cover_passed
    
    if all_passed:
        print("✅ All indexing system tests PASSED!")
        print("✅ Ready to test with brochure generation!")
        print("\n🔧 Next steps:")
        print("1. Run: python generate_brochures.py english")
        print("2. Check that products are sorted by index (PC before LC)")
        print("3. Check that brand-specific covers are used")
        print("4. Verify new PAGE naming convention works")
    else:
        print("❌ Some indexing system tests FAILED!")
        print("❌ Please fix the issues before proceeding")

if __name__ == "__main__":
    main()
