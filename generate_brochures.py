#!/usr/bin/env python3
"""
Enhanced Brochure Generator Script

This script generates PDF brochures with the following enhancements:
1. Language Support: Arabic and English versions
2. Front Cover Pages: Multiple front cover images
3. Category Cover Images: Dedicated cover for each category
4. End Page: Final page with end image
5. Improved Sorting: Packed items first, then by category and brand
6. Arabic Font Support: Proper RTL text rendering

Usage:
    python generate_brochures.py [language]
    
    language: 'arabic' (default), 'english', or 'both'
"""

import sys
import os
from fetch_data import generate_brochure

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("🏪 ENHANCED PDF BROCHURE GENERATOR")
    print("=" * 60)
    print("Features:")
    print("  📚 Multi-language support (Arabic/English)")
    print("  🖼️  Front cover pages")
    print("  🏷️  Category cover images")
    print("  🔚 End page")
    print("  📊 Smart product sorting")
    print("  🔤 Arabic font support")
    print("=" * 60)

def check_prerequisites():
    """Check if required folders and files exist"""
    print("🔍 Checking prerequisites...")
    
    issues = []
    
    # Check img folder
    img_folder = "img"
    if not os.path.exists(img_folder):
        issues.append(f"❌ Image folder '{img_folder}' not found")
    else:
        images = [f for f in os.listdir(img_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        if not images:
            issues.append(f"⚠️  No images found in '{img_folder}' folder")
        else:
            print(f"✅ Found {len(images)} images in '{img_folder}' folder")
    
    # Check fonts folder
    fonts_folder = "fonts"
    if not os.path.exists(fonts_folder):
        issues.append(f"⚠️  Fonts folder '{fonts_folder}' not found (will use system fonts)")
    else:
        fonts = [f for f in os.listdir(fonts_folder) if f.lower().endswith('.ttf')]
        print(f"✅ Found {len(fonts)} fonts in '{fonts_folder}' folder")
    
    # Check data file
    data_file = "res_full.json"
    if not os.path.exists(data_file):
        issues.append(f"❌ Data file '{data_file}' not found")
    else:
        print(f"✅ Data file '{data_file}' found")
    
    if issues:
        print("\n⚠️  Issues found:")
        for issue in issues:
            print(f"  {issue}")
        print()
    
    return len([issue for issue in issues if issue.startswith("❌")]) == 0

def generate_arabic_brochure():
    """Generate Arabic brochure"""
    print("🇸🇦 Generating Arabic brochure...")
    output_file = "Product_Brochure_Arabic.pdf"
    
    success = generate_brochure(output_file=output_file, language="arabic")
    
    if success:
        print(f"✅ Arabic brochure generated: {output_file}")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"   📄 File size: {file_size:.2f} MB")
    else:
        print("❌ Failed to generate Arabic brochure")
    
    return success

def generate_english_brochure():
    """Generate English brochure"""
    print("🇺🇸 Generating English brochure...")
    output_file = "Product_Brochure_English.pdf"
    
    success = generate_brochure(output_file=output_file, language="english")
    
    if success:
        print(f"✅ English brochure generated: {output_file}")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"   📄 File size: {file_size:.2f} MB")
    else:
        print("❌ Failed to generate English brochure")
    
    return success

def main():
    """Main function"""
    print_banner()
    
    # Parse command line arguments
    language = "arabic"  # default
    if len(sys.argv) > 1:
        language = sys.argv[1].lower()
    
    if language not in ["arabic", "english", "both"]:
        print("❌ Invalid language. Use 'arabic', 'english', or 'both'")
        sys.exit(1)
    
    print(f"🌐 Language mode: {language}")
    print()
    
    # Check prerequisites
    if not check_prerequisites():
        print("❌ Prerequisites check failed. Please fix the issues above.")
        sys.exit(1)
    
    print("✅ Prerequisites check passed")
    print()
    
    # Generate brochures
    results = []
    
    try:
        if language in ["arabic", "both"]:
            results.append(("Arabic", generate_arabic_brochure()))
            print()
        
        if language in ["english", "both"]:
            results.append(("English", generate_english_brochure()))
            print()
        
        # Summary
        print("=" * 60)
        print("📊 GENERATION SUMMARY")
        print("=" * 60)
        
        success_count = 0
        for lang, success in results:
            status = "✅ Success" if success else "❌ Failed"
            print(f"{lang} brochure: {status}")
            if success:
                success_count += 1
        
        print(f"\n🎯 {success_count}/{len(results)} brochures generated successfully")
        
        if success_count > 0:
            print("\n🎉 Brochure generation completed!")
            print("\n📋 Enhanced features included:")
            print("  • Language-specific content filtering")
            print("  • Front cover pages (img/front-*.jpg)")
            print("  • Category cover images")
            print("  • End page (img/end.jpg)")
            print("  • Hierarchical sorting (packed → category → brand)")
            print("  • Arabic font support with RTL text")
            print("  • Fuzzy matching for category images")
        else:
            print("\n❌ No brochures were generated successfully.")
            print("Please check the error messages above and ensure:")
            print("  • WooCommerce API credentials are correct")
            print("  • Internet connection is available")
            print("  • Required files and folders exist")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Generation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
