<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Brochure</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #d21f3c;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-box {
            background-color: #f9f9f9;
            border-left: 4px solid #d21f3c;
            padding: 15px;
            margin-bottom: 20px;
        }
        .button-container {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #d21f3c;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #b01933;
        }
        .button.secondary {
            background-color: #555;
        }
        .button.secondary:hover {
            background-color: #333;
        }
        .pdf-frame {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            margin-top: 20px;
            display: none;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>The Premium Harvest Product Brochure</h1>
        
        <div class="info-box">
            <h3>Brochure Information</h3>
            <p><strong>Status:</strong> {% if pdf_info.exists %}Available{% else %}Not generated yet{% endif %}</p>
            <p><strong>Last Modified:</strong> {{ pdf_info.last_modified }}</p>
            <p><strong>File Size:</strong> {{ pdf_info.size }}</p>
        </div>
        
        <div class="button-container">
            <a href="{{ url_for('download_pdf') }}" class="button">Download PDF</a>
            <a href="{{ url_for('view_pdf') }}" class="button" target="_blank">View PDF</a>
            <a href="{{ url_for('refresh_pdf') }}" class="button secondary">Refresh PDF</a>
        </div>
        
        <div class="footer">
            <p>This brochure is automatically generated from your WooCommerce product catalog.</p>
            <p>Click "Refresh PDF" to update the brochure with the latest product information.</p>
        </div>
    </div>
</body>
</html>
