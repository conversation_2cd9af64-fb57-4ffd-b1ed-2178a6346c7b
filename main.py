from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib import colors
import os
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph

from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import os

# Path to the fonts folder
fonts_path = "fonts/"

# Register Satoshi fonts
pdfmetrics.registerFont(TTFont("Satoshi-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))
pdfmetrics.registerFont(TTFont("Satoshi-Bold", os.path.join(fonts_path, "Satoshi-Bold.ttf")))
pdfmetrics.registerFont(TTFont("Satoshi-Light", os.path.join(fonts_path, "Satoshi-Light.ttf")))
pdfmetrics.registerFont(TTFont("Satoshi-Medium", os.path.join(fonts_path, "Satoshi-Medium.ttf")))
pdfmetrics.registerFont(TTFont("Satoshi-Black", os.path.join(fonts_path, "Satoshi-Black.ttf")))






def draw_custom_rect_with_cover(c, x, y, width, height, radius, fill_color=(0.925, 0.129, 0.165), border_color=(1, 0, 0)):
    # Draw the rounded rectangle
    c.setFillColorRGB(*fill_color)  # Fill color
    c.roundRect(x, y, width, height, radius, stroke=0, fill=1)  # No border

    # Cover the left rounded corners with a sharp-cornered square
    c.setFillColorRGB(*fill_color)  # Same color as the rectangle
    c.rect(x, y, radius, height, stroke=0, fill=1)  # Sharp-cornered rectangle on the left side

def create_brochure_template(output_file):
    # Initialize the canvas
    c = canvas.Canvas(output_file, pagesize=A4)
    page_width, page_height = A4

    # Margins and grid setup
    margin_x = 0.5 * inch
    margin_y = 1 * inch
    grid_width = (page_width - 2 * margin_x) / 4  # Four columns
    grid_height = ((page_height - 2 * margin_y - 1.5 * inch) / 4) + .1 * inch  # Add 0.2 inch padding

    # Header Section
    c.setFont("Satoshi-Bold", 30)
    c.drawCentredString(page_width / 2, page_height - margin_y, "The Premium Harvest")

    # that gray line on right 
    c.setFillColorRGB(0.949, 0.953, 0.957)  # Light gray color
    line_width = 30  # Width of the vertical line
    c.rect(0, 0, line_width, page_height, stroke=0, fill=1)



    # Sub-header (Category)
    subcategory_text = "Dried Fruitssss"
    text_width = c.stringWidth(subcategory_text, "Satoshi-Bold", 13) + 1 * inch  # Add padding for flexibility
    x = 0
    y = page_height - margin_y - 0.8 * inch
    width = text_width
    height = 0.4 * inch
    radius = 0.2 * inch
    draw_custom_rect_with_cover(c, x, y, width, height, radius, fill_color=(0.925, 0.129, 0.165))

    # Add text inside the rectangle
    c.setFillColorRGB(1, 1, 1)  # White text color
    c.setFont("Satoshi-Regular", 13)
    c.drawString(x + .7 * inch, y + 0.15 * inch, subcategory_text)  # Adjust padding for text alignment




    # # Sub-header (Category)
    # x = 0
    # y = page_height - margin_y - 0.6 * inch
    # width = 2 * inch
    # height = 0.4 * inch
    # radius = 0.2 * inch
    # draw_custom_rect_with_cover(c, x, y, width, height, radius, fill_color=(0.925, 0.129, 0.165))

    # # Add text inside the rectangle
    # c.setFillColorRGB(1, 1, 1)  # White text color
    # c.setFont("Helvetica", 13)
    # c.drawString(x + 0.5 * inch, y + 0.15 * inch, "Dried Fruits")

    # Placeholder Grid
    c.setFont("Satoshi-Regular", 10)
    styles = getSampleStyleSheet()
    style = styles["Normal"]
    style.alignment = 1  # Center alignment
    style.fontName = "Satoshi-Regular"
    style.fontSize = 9

    for row in range(4):
        for col in range(4):
            x = margin_x + col * grid_width
            y = page_height - margin_y - (row + 1) * grid_height - 1.5 * inch

            # Product image placeholder
            image_path = "img/0103004 (1).png"  # Path to the placeholder image
            img_width = grid_width - 10
            img_height = 1.1 * inch
            if os.path.exists(image_path):
                try:
                    c.drawImage(image_path, x + 0.1 * inch, y + grid_height - 1.2 * inch, width=img_width, height=img_height, preserveAspectRatio=True, mask='auto')
                except Exception as e:
                    print(f"Error loading image {image_path}: {e}")
            else:
                print(f"Image not found: {image_path}")

            # Text on image (e.g., weight in a black circle at the top-right corner)
            c.setFillColorRGB(0, 0, 0)  # Black circle
            c.circle(x + grid_width - 0.4 * inch, y + grid_height - 0.2 * inch, 0.18 * inch, fill=1)
            c.setFillColorRGB(1, 1, 1)  # White text
            c.setFont("Satoshi-Bold", 9)
            c.drawCentredString(x + grid_width - 0.4 * inch, y + grid_height - 0.23 * inch, "250g")

            # Packaging circle (e.g., "Pkg 24") on the left side of the image
            c.setFillColorRGB(0.902, 0.906, 0.914) 

            c.circle(x + 0.35 * inch, y +   1.14 * inch, 0.18 * inch, fill=1,stroke=0)
            c.setFillColorRGB(0, 0, 0)  # Black text
            c.setFont("Satoshi-Regular", 8)
            c.drawCentredString(x + 0.35 * inch, y + grid_height - 0.55 - .98* inch, "Pkg")
            c.drawCentredString(x + 0.35 * inch, y + grid_height - 0.7- 1.1 * inch, "24")

            # Product Name
            text = "Dehydrated Tropic Mix looooooon"
            para = Paragraph(text, style)
            text_width = grid_width - 0.4 * inch  # Subtract padding
            para_width, para_height = para.wrap(text_width, y)  # Wrap the text to the grid width
            para.drawOn(c, x + (grid_width - text_width) / 2, y + grid_height - 1.2 * inch - para_height)

            # Dynamically calculate the position for the country box
            country_box_y = y + grid_height - 1.2 * inch - para_height - 0.2 * inch  # Add some padding below the name text

            # Country of Origin (e.g., "Thailand" in a red rounded rectangle below the name)
            CountryofOrigin = "Thailand"
            c.setFillColorRGB(0, 0, 0)  # Red background
            text_width = c.stringWidth(CountryofOrigin, "Satoshi-Regular", 8) + 0.1 * inch
            c.roundRect(x + grid_width / 2 - text_width / 2, country_box_y, text_width, 0.15 * inch, 0.02 * inch, fill=1,stroke=0)
            c.setFillColorRGB(1, 1, 1)  # White text
            c.setFont("Satoshi-Regular", 8)
            c.drawCentredString(x + grid_width / 2, country_box_y + 0.04 * inch, CountryofOrigin)

    # Save the canvas
    c.save()

# Output file for the template
output_file = "D:/Emilda/IDTC/brochure/Styled_Brochure_Template.pdf"
create_brochure_template(output_file)
