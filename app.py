from flask import Flask, send_file, render_template, redirect, url_for
import os
import time
from datetime import datetime
from fetch_data import generate_brochure

app = Flask(__name__)

# Configuration
PDF_DIRECTORY = os.path.dirname(os.path.abspath(__file__))
PDF_FILENAME = "Product_Brochure.pdf"
PDF_PATH = os.path.join(PDF_DIRECTORY, PDF_FILENAME)
CACHE_INFO_PATH = os.path.join(PDF_DIRECTORY, "cache_info.txt")

def get_pdf_info():
    """Get information about the PDF file"""
    if os.path.exists(PDF_PATH):
        # Get file modification time
        mod_time = os.path.getmtime(PDF_PATH)
        mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
        file_size = os.path.getsize(PDF_PATH) / (1024 * 1024)  # Size in MB
        
        return {
            "exists": True,
            "last_modified": mod_time_str,
            "size": f"{file_size:.2f} MB"
        }
    else:
        return {
            "exists": False,
            "last_modified": "N/A",
            "size": "N/A"
        }

def save_generation_info():
    """Save information about when the PDF was generated"""
    with open(CACHE_INFO_PATH, 'w') as f:
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

@app.route('/')
def index():
    """Main page with PDF info and download/refresh options"""
    pdf_info = get_pdf_info()
    return render_template('index.html', pdf_info=pdf_info)

@app.route('/view-pdf')
def view_pdf():
    """View the PDF in the browser"""
    if not os.path.exists(PDF_PATH):
        # Generate the PDF if it doesn't exist
        generate_brochure()
        save_generation_info()
    
    # Serve the PDF file with content_type set to application/pdf
    return send_file(PDF_PATH, mimetype='application/pdf')

@app.route('/download-pdf')
def download_pdf():
    """Download the PDF file"""
    if not os.path.exists(PDF_PATH):
        # Generate the PDF if it doesn't exist
        generate_brochure()
        save_generation_info()
    
    # Serve the PDF file as an attachment for download
    return send_file(PDF_PATH, as_attachment=True)

@app.route('/refresh-pdf')
def refresh_pdf():
    """Regenerate the PDF file"""
    try:
        # Generate a new PDF
        generate_brochure()
        save_generation_info()
        return redirect(url_for('index'))
    except Exception as e:
        return f"Error refreshing PDF: {str(e)}", 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
