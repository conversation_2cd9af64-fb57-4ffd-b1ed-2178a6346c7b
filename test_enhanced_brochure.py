#!/usr/bin/env python3
"""
Test script for the enhanced PDF brochure generator.
This script demonstrates the new features including language support,
front/end pages, category covers, and improved sorting.
"""

import os
import sys
from fetch_data import generate_brochure

def test_arabic_brochure():
    """Generate Arabic brochure"""
    print("=" * 50)
    print("Generating Arabic Brochure")
    print("=" * 50)
    
    output_file = "Product_Brochure_Arabic_Test.pdf"
    success = generate_brochure(output_file=output_file, language="arabic")
    
    if success:
        print(f"✅ Arabic brochure generated successfully: {output_file}")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # Size in MB
            print(f"📄 File size: {file_size:.2f} MB")
    else:
        print("❌ Failed to generate Arabic brochure")
    
    return success

def test_english_brochure():
    """Generate English brochure"""
    print("=" * 50)
    print("Generating English Brochure")
    print("=" * 50)
    
    output_file = "Product_Brochure_English_Test.pdf"
    success = generate_brochure(output_file=output_file, language="english")
    
    if success:
        print(f"✅ English brochure generated successfully: {output_file}")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # Size in MB
            print(f"📄 File size: {file_size:.2f} MB")
    else:
        print("❌ Failed to generate English brochure")
    
    return success

def check_image_folder():
    """Check if image folder exists and list available images"""
    print("=" * 50)
    print("Checking Image Folder")
    print("=" * 50)
    
    img_folder = "img"
    if os.path.exists(img_folder):
        images = [f for f in os.listdir(img_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        print(f"📁 Found {len(images)} images in '{img_folder}' folder:")
        
        # Categorize images
        front_images = [img for img in images if img.startswith('front-')]
        category_images = [img for img in images if not img.startswith('front-') and img != 'end.jpg']
        end_images = [img for img in images if img == 'end.jpg']
        
        print(f"  🖼️  Front covers: {len(front_images)} ({', '.join(front_images)})")
        print(f"  🏷️  Category covers: {len(category_images)} ({', '.join(category_images)})")
        print(f"  🔚 End page: {len(end_images)} ({', '.join(end_images)})")
        
        return True
    else:
        print(f"❌ Image folder '{img_folder}' not found")
        return False

def check_fonts():
    """Check if fonts are available"""
    print("=" * 50)
    print("Checking Fonts")
    print("=" * 50)
    
    fonts_folder = "fonts"
    if os.path.exists(fonts_folder):
        fonts = [f for f in os.listdir(fonts_folder) if f.lower().endswith('.ttf')]
        print(f"📝 Found {len(fonts)} fonts in '{fonts_folder}' folder:")
        for font in fonts:
            print(f"  - {font}")
    else:
        print(f"⚠️  Fonts folder '{fonts_folder}' not found")
    
    # Check system fonts for Arabic support
    system_fonts = [
        "C:/Windows/Fonts/tahoma.ttf",
        "/System/Library/Fonts/Arial Unicode MS.ttf"
    ]
    
    print("\n🔍 Checking system fonts for Arabic support:")
    for font_path in system_fonts:
        if os.path.exists(font_path):
            print(f"  ✅ {font_path}")
        else:
            print(f"  ❌ {font_path}")

def main():
    """Main test function"""
    print("🚀 Enhanced PDF Brochure Generator Test")
    print("This script tests the new features:")
    print("  • Language support (Arabic/English)")
    print("  • Front cover pages")
    print("  • Category cover images")
    print("  • End page")
    print("  • Improved product sorting")
    print("  • Arabic font support")
    print()
    
    # Check prerequisites
    check_image_folder()
    print()
    check_fonts()
    print()
    
    # Test brochure generation
    try:
        # Test Arabic brochure
        arabic_success = test_arabic_brochure()
        print()
        
        # Test English brochure
        english_success = test_english_brochure()
        print()
        
        # Summary
        print("=" * 50)
        print("Test Summary")
        print("=" * 50)
        print(f"Arabic brochure: {'✅ Success' if arabic_success else '❌ Failed'}")
        print(f"English brochure: {'✅ Success' if english_success else '❌ Failed'}")
        
        if arabic_success or english_success:
            print("\n🎉 At least one brochure was generated successfully!")
            print("📋 New features implemented:")
            print("  ✅ Language-specific content filtering")
            print("  ✅ Front cover pages from img/front-*.jpg")
            print("  ✅ Category cover images")
            print("  ✅ End page from img/end.jpg")
            print("  ✅ Hierarchical sorting (packed → category → brand)")
            print("  ✅ Arabic font support")
            print("  ✅ Fuzzy matching for category images")
        else:
            print("\n❌ Both brochure generations failed. Please check the error messages above.")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
