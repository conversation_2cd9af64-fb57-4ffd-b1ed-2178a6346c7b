<?php
/**
 * Plugin Name: Safe WooCommerce API Authentication
 * Description: Enhanced authentication for WooCommerce API with proper error handling
 * Version: 1.0
 * Author: Your Name
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeWooCommerceAuth {
    
    public function __construct() {
        // Only add filters if we're dealing with REST API requests
        add_action('rest_api_init', array($this, 'init_auth_filters'));
    }
    
    public function init_auth_filters() {
        // Only apply authentication for specific endpoints
        add_filter('determine_current_user', array($this, 'authenticate_user'), 20);
        add_filter('rest_authentication_errors', array($this, 'check_permissions'), 20);
    }
    
    public function authenticate_user($user) {
        // Only process if we don't already have a user and we have auth headers
        if ($user || empty($_SERVER['PHP_AUTH_USER']) || empty($_SERVER['PHP_AUTH_PW'])) {
            return $user;
        }
        
        // Check if we're on a WooCommerce or taxonomy endpoint
        if (!$this->is_relevant_endpoint()) {
            return $user;
        }
        
        // Check if Application Passwords function exists (WordPress 5.6+)
        if (!function_exists('wp_authenticate_application_password')) {
            error_log('WooCommerce Auth Plugin: wp_authenticate_application_password function not available');
            return $user;
        }
        
        try {
            // Try to authenticate via Application Passwords
            $auth_user = wp_authenticate_application_password(
                sanitize_user($_SERVER['PHP_AUTH_USER']), 
                $_SERVER['PHP_AUTH_PW']
            );
            
            if (is_wp_error($auth_user)) {
                error_log('WooCommerce Auth Plugin: Authentication failed - ' . $auth_user->get_error_message());
                return null;
            }
            
            if ($auth_user && is_object($auth_user) && isset($auth_user->ID)) {
                error_log('WooCommerce Auth Plugin: User authenticated successfully - ID: ' . $auth_user->ID);
                return $auth_user;
            }
            
        } catch (Exception $e) {
            error_log('WooCommerce Auth Plugin: Exception during authentication - ' . $e->getMessage());
        }
        
        return $user;
    }
    
    public function check_permissions($result) {
        // If there's already an error, return it
        if (is_wp_error($result)) {
            return $result;
        }
        
        // If authentication was successful, don't interfere
        if (false !== $result) {
            return $result;
        }
        
        // Only check permissions for specific endpoints
        if (!$this->is_relevant_endpoint()) {
            return $result;
        }
        
        // For WooCommerce endpoints, check if user can manage WooCommerce
        if ($this->is_woocommerce_endpoint()) {
            if (!current_user_can('manage_woocommerce')) {
                return new WP_Error(
                    'rest_cannot_access_woocommerce', 
                    'You do not have permission to access WooCommerce data.', 
                    array('status' => 401)
                );
            }
        }
        
        // For taxonomy endpoints, check if user can edit terms
        if ($this->is_taxonomy_endpoint()) {
            if (!current_user_can('edit_terms')) {
                return new WP_Error(
                    'rest_cannot_edit_terms', 
                    'You do not have permission to edit terms.', 
                    array('status' => 401)
                );
            }
        }
        
        return $result;
    }
    
    private function is_relevant_endpoint() {
        return $this->is_woocommerce_endpoint() || $this->is_taxonomy_endpoint();
    }
    
    private function is_woocommerce_endpoint() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($request_uri, '/wp-json/wc/') !== false;
    }
    
    private function is_taxonomy_endpoint() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($request_uri, '/wp-json/wp/v2/') !== false && 
               (strpos($request_uri, 'categories') !== false || 
                strpos($request_uri, 'tags') !== false ||
                strpos($request_uri, 'product_cat') !== false ||
                strpos($request_uri, 'product_tag') !== false);
    }
}

// Initialize the plugin
new SafeWooCommerceAuth();

// Add activation hook to check requirements
register_activation_hook(__FILE__, function() {
    if (version_compare(get_bloginfo('version'), '5.6', '<')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die('This plugin requires WordPress 5.6 or higher for Application Password support.');
    }
});
