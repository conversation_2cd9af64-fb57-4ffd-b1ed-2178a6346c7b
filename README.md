# Product Brochure Web Application

This web application provides a simple interface to view, download, and refresh a product brochure PDF generated from WooCommerce product data.

## Features

- View the product brochure directly in the browser
- Download the product brochure as a PDF file
- Refresh the brochure to include the latest product data
- Display information about the PDF (last modified date, file size)

## Setup and Installation

### Local Development

1. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Run the Flask application:
   ```
   python app.py
   ```

3. Open your browser and navigate to `http://localhost:5000`

### Deployment Options

#### Render (Free Tier)

1. Create a new account on [Render](https://render.com/)
2. Create a new Web Service
3. Connect your GitHub repository
4. Configure the service:
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `gunicorn app:app`
5. Deploy the application

#### PythonAnywhere (Free Tier)

1. Create a new account on [PythonAnywhere](https://www.pythonanywhere.com/)
2. Upload your code or clone your repository
3. Create a new web app with Flask
4. Configure the WSGI file to point to your app
5. Set up the virtual environment with the required dependencies

#### Heroku (Free Tier Alternatives)

Since Heroku removed their free tier, consider these alternatives:
- [Fly.io](https://fly.io/) - Generous free tier
- [Railway](https://railway.app/) - Limited free tier
- [Koyeb](https://www.koyeb.com/) - Limited free tier

## Usage

1. **View PDF**: Click the "View PDF" button to open the brochure in a new browser tab
2. **Download PDF**: Click the "Download PDF" button to download the brochure to your device
3. **Refresh PDF**: Click the "Refresh PDF" button to regenerate the brochure with the latest product data

## Notes

- The PDF is generated using the ReportLab library
- Product data is fetched from the WooCommerce API
- The application caches the PDF to avoid regenerating it on every request
- The refresh button allows you to update the PDF when product data changes
